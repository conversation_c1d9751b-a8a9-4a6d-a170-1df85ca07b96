---
type: "agent_requested"
description: "Example description"
---
## Отправить сообщение

`POST` `https://app.leadteh.ru/api/v1/sendMessage`

Этот метод позволяет отправить сообщение по ID контакта.  
(Отправка в запросе данных контакта должна быть **отключена**)

### Query Parameters

| Name                 | Type    | Description                                                                 |
|----------------------|---------|-----------------------------------------------------------------------------|
| `file`               | string  | URL на файл                                                                 |
| `image`              | string  | URL на изображение                                                          |
| `contact_id`         | integer | ID контакта                                                                 |
| `text`               | string  | Текст сообщения                                                             |
| `messenger`          | string  | ID мессенджера (обязательно, если не указан `contact_id`)                  |
| `bot_id`             | integer | ID бота (обязательно, если не указан `contact_id`)                         |
| `contact_external_id`| string  | Внешний ID контакта (обязательно, если не указан `contact_id`)             |

### Responses

#### 200 OK

```json
{
  "success": true
}
```

#### 403 Forbidden

```json
{
    "message": "Forbidden"
}
```

#### 422 Переданные данные некорректны.

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "contact_id": [
      "Поле contact id обязательно для заполнения, когда messenger \/ bot id \/ contact external id не указано."
    ],
    "text": [
      "Поле text обязательно для заполнения, когда ни одно из image \/ file не указано."
    ],
    "image": [
      "Поле image обязательно для заполнения, когда ни одно из text \/ file не указано."
    ],
    "file": [
      "Поле file обязательно для заполнения, когда ни одно из text \/ image не указано."
    ],
    "messenger": [
      "Поле messenger обязательно для заполнения, когда contact id не указано."
    ],
    "bot_id": [
      "Поле bot id обязательно для заполнения, когда contact id не указано."
    ],
    "contact_external_id": [
      "Поле contact external id обязательно для заполнения, когда contact id не указано."
    ]
  }
}
```

#### 429 Превышен лимит отправки сообщений.

```json
{
    "error": "Достигнут лимит отправки сообщений для whatsapp"
}
```

#### 501 Отправка для мессенджера еще контакта не реализована. 

```json
{
    "error": "На данный момент не реализована отправка сообщений в мессенджер контакта (icq)"
}
```


## Отправить сообщение по внешнему ID

`POST` `https://app.leadteh.ru/api/v1/sendMessage`

Этот метод позволяет отправить сообщение по номеру телефона или внешнему ID контакта в мессенджере или соцсети.  
(Отправка в запросе данных контакта должна быть **отключена**)

### Query Parameters

| Name                 | Type    | Description                                                      |
|----------------------|---------|------------------------------------------------------------------|
| `file`               | string  | URL на файл                                                      |
| `image`              | string  | URL на изображение                                               |
| `bot_id`             | integer | ID бота                                                          |
| `contact_external_id`| string  | Номер телефона или внешний ID контакта в мессенджере             |
| `messenger`          | string  | ID мессенджера                                                   |
| `text`               | string  | Текст сообщения                                                  |

### Responses

#### `200` OK

```json
{
  "success": true
}
```

#### `403` Forbidden

```json
{
  "message": "Forbidden"
}
```

#### `422` Validation Error

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "contact_external_id": [
      "Поле contact external id обязательно для заполнения."
    ],
    "text": [
      "Поле text обязательно для заполнения, когда ни одно из image / file не указано."
    ]
  }
}
```

#### `429` Too Many Requests

```json
{
    "error": "Достигнут лимит отправки сообщений для whatsapp"
}
```

#### `501` Not Implemented

```json
{
  "error": "На данный момент не реализована отправка сообщений в мессенджер контакта (icq)"
}
```

Поле `messenger` может принимать следующие значения:

- `whatsapp`
- `telegram`
- `viber`
- `icq`

Поле `contact_external_id` может содержать номер телефона, если он привязан к контакту. Привязка происходит, например, при первом платеже клиента.

> ⚠️ Ваш `contact_external_id` должен быть в контактах у бота. Напишите боту с нужного мессенджера.  
> Отправка на произвольный номер возможна **только через WhatsApp** с использованием метода `sendMessageToWhatsApp`.

### Лимиты

| Мессенджер | Сообщений за 10 сек. |
|------------|-----------------------|
| WhatsApp   | 1                     |
| Telegram   | 10                    |
| Viber      | 10                    |
| ICQ        | 10                    |


## Отправить сообщение в WhatsApp

`POST` `https://app.leadteh.ru/api/v1/sendMessageToWhatsApp`

Этот метод позволяет отправить сообщение на WhatsApp по номеру телефона.
(Отправка в запросе данных контакта должна быть ОТКЛЮЧЕНА)

#### Request Body

| Name    | Type    | Description                                                                       |
| ------- | ------- | --------------------------------------------------------------------------------- |
| bot\_id | integer | ID бота контакта.                                                                 |
| phone   | string  | Номер телефона                                                                    |
| text    | string  | Сообщение                                                                         |
| name    | string  | Имя контакта, необходимо отправлять когда вы пишите данному контакту в первый раз |


#### Reponse `200`

```json
{
    "data": {
          "id": 1,
          "phone": "79991234567",
          "name": "Иван Иванов",
          "messenger": "whatsapp",
          "created_at": "2019-05-10T10:38:28+00:00"
    }
}
```

