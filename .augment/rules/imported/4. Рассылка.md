---
type: "agent_requested"
description: "Example description"
---
# Рассылка

Все отправленные сообщения добавляются в очередь и отправляются согласно лимитам в мессенджерах.

## Отправить текстовое сообщение

```
POST https://app.leadteh.ru/api/v1/sendMessageToQueue
```

#### Request Body

| Name                | Type    | Description                                                                                 |
| ------------------- | ------- | ------------------------------------------------------------------------------------------- |
| message\_id         | string  | Уникальный id сообщения                                                                     |
| text                | string  | Текст сообщения                                                                             |
| contact\_id         | integer | ID контакта LeadTex. Обязательный параметр когда `bitrix_contact_id` и `phone` не переданы. |
| bitrix\_contact\_id | integer | ID контакта Битрикса. Обязательный параметр когда `contact_id` и `phone` не переданы.       |
| phone               | string  | Номер телефона. Обязательный параметр когда `bitrix_contact_id` и `contact_id` не переданы. |

---

#### `201` Сообщение успешно добавлено в очередь на отправку

```json
// тело ответа отсутствует или пустое
```
