---
type: "agent_requested"
description: "Example description"
---
# Счета

С помощью представленных методов вы можете создавать счета для ваших контактов в рамках ISO 4217, а так же проводить операции по ним — начисление/списание.

**Внимание!**

Данное API вы используете на свой страх и риск, мы не несем ответственности за сохранность данных созданных вами счетов через представленное API, но приложим все усилия для их сохранности и безопасности. Вы не должны использовать методы API представленные на этой странице, если их использование нарушает законодательство Российской Федерации, Европейского союза и США.

## Получить список счетов

`GET` `https://app.leadteh.ru/api/v1/getContactAccounts`

Этот метод позволяет получить список счетов указанного контакта.  
(Отправка в запросе данных контакта должна быть **ОТКЛЮЧЕНА**)

#### Query Parameters

| Name       | Type    | Description  |
|------------|---------|--------------|
| contact_id | integer | ID контакта  |

---

### 200 Запрос успешно обработан.

```json
{
  "data": [
    {
      "id": 1,
      "currency": "USD",
      "amount": 17500,
      "amount_note": "175 USD",
      "created_at": "2019-11-29T13:33:35+00:00",
      "updated_at": "2019-11-30T07:08:57+00:00"
    }
  ]
}


## Создать счет

`POST` `https://app.leadteh.ru/api/v1/addContactAccount`

Этот метод позволяет создать счет для указанного контакта.  
(Отправка в запросе данных контакта должна быть **ОТКЛЮЧЕНА**)

#### Request Body

| Name       | Type    | Description                                    |
|------------|---------|------------------------------------------------|
| contact_id | integer | ID контакта.                                   |
| currency   | string  | Трехзначный код валюты в ISO 4217. Пример: USD |


### 200 Запрос успешно обработан.

```json
{
  "data": {
    "id": 6,
    "currency": "USD",
    "amount": 0,
    "amount_note": "0 USD",
    "created_at": "2019-11-30T14:56:24+00:00",
    "updated_at": "2019-11-30T14:56:24+00:00"
  }
}
```

### 422 Аккаунт уже существует.

```json
{
  "errors": {
    "currency": [
      "Account with the currency already exists"
    ]
  }
}
```

### 422 Неподдерживаемый формат

```json
{
  "message": "The given data was invalid.",
  "errors": {
    "currency": [
      "The currency format is invalid.",
      "The selected currency is invalid."
    ]
  }
}
```


## Удалить счет

`POST` `https://app.leadteh.ru/api/v1/deleteContactAccount`

Этот метод позволяет удалить счет контакта.  
(Отправка в запросе данных контакта должна быть **ОТКЛЮЧЕНА**)

#### Request Body

| Name       | Type    | Description |
|------------|---------|-------------|
| account_id | integer | ID счета.   |


### 204 Счет успешно удален

```json
// пустой ответ
```

### 422 Счет не может быть удален, т.к. имеет положительный баланс.

```json
{
  "errors": {
    "account_id": [
      "You can not delete the account with a balance of 175 RUB"
    ]
  }
}
```

> **Внимание:**  
> Счет может быть удален только если имеет нулевой баланс.


## Зачислить сумму на счет

`POST` `https://app.leadteh.ru/api/v1/addFundsToContactAccount`

Этот метод позволяет зачислить средства на счет контакта.  
(Отправка в запросе данных контакта должна быть ОТКЛЮЧЕНА)

#### Request Body

| Name        | Type    | Description                                            |
|-------------|---------|--------------------------------------------------------|
| account_id* | integer | ID счета                                               |
| amount*     | integer | Сумма в минимальной денежной единице. Например для $10 - это 1000 |
| description*| string  | Описание транзакции                                    |

_* — обязательные поля_

#### `200` Счет успешно пополнен

```json
{
  "data": {
    "id": 1,
    "currency": "USD",
    "amount": 117500,
    "amount_note": "1175 USD",
    "created_at": "2019-11-29T13:33:35+00:00",
    "updated_at": "2019-11-30T07:08:57+00:00"
  }
}
```

## Списать сумму со счета

`POST` `https://app.leadteh.ru/api/v1/withdrawFundsFromContactAccount`

Этот метод позволяет списать средства со счета контакта.  
(Отправка в запросе данных контакта должна быть ОТКЛЮЧЕНА)

#### Request Body

| Name        | Type   | Description                                            |
|-------------|--------|--------------------------------------------------------|
| account_id* | String | ID счета                                               |
| amount*     | String | Сумма в минимальной денежной единице. Например для $10 - это 1000 |
| description*| String | Описание транзакции                                    |

_* — обязательные поля_

#### `200` Счет успешно пополнен

```json
// пустой ответ
```
