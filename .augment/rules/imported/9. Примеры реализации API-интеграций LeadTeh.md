---
type: "agent_requested"
description: "Example description"
---
# 📘 Примеры реализации API-интеграций LeadTeh

## 🔑 Основы

* **Token** – токен для взаимодействия с API.
  Получить можно в разделе: [https://app.leadteh.ru/settings](https://app.leadteh.ru/settings)

## 🧾 Дополнительные параметры

| Параметр     | Описание                          |
| ------------ | --------------------------------- |
| `bot_id`     | Внутренний ID бота                |
| `contact_id` | ID контакта из списка "Контакты"  |
| `tag`        | Значение тега                     |
| `value`      | Значение переменной               |
| `*name`      | Имя переменной (для пользователя) |

> \* Переменные пользователя обозначаются звёздочкой

---

## 📥 Получение данных списка контактов

**Метод:** `GET`

```php
function getContacts($token) {
    $apiUrl = "https://app.leadteh.ru/api/v1/getContacts?api_token=$token";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        echo 'Ошибка при получении контактов.';
    } else {
        $contacts = json_decode($response, true);
        echo json_encode($contacts);
    }
}
getContacts($token);
?>
```

---

## ➕ Добавление тега контакту

**Метод:** `POST`

```php
function addTagToContact($token, $contact_id, $tag) {
    $apiUrl = "https://app.leadteh.ru/api/v1/attachTagToContact?api_token=$token&contact_id=$contact_id&name=$tag";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        echo 'Ошибка при добавлении тега.';
    } else {
        $result = json_decode($response, true);
        echo json_encode($result);
    }
}
addTagToContact($token, $contact_id, $tag);
?>
```

---

## ❌ Удаление тега у контакта

**Метод:** `GET`

```php
function removeTagFromContact($token, $contact_id, $tag) {
    $apiUrl = "https://app.leadteh.ru/api/v1/detachTagFromContact?api_token=$token&contact_id=$contact_id&name=$tag";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        echo 'Ошибка при удалении тега.';
    } else {
        $result = json_decode($response, true);
        echo json_encode($result);
    }
}
removeTagFromContact($token, $contact_id, $tag);
?>
```

---

## 🏷 Получение тегов контакта

**Метод:** `GET`

```php
function getContactTags($token, $contact_id) {
    $apiUrl = "https://app.leadteh.ru/api/v1/getContactTags?contact_id=$contact_id&api_token=$token";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        echo 'Ошибка при получении тегов.';
    } else {
        $result = json_decode($response, true);
        echo json_encode($result);
    }
}
getContactTags($token, $contact_id);
?>
```

---

## 📄 Получение переменных пользователя

**Метод:** `POST`

```php
function getContactVariables($token, $contact_id) {
    $apiUrl = "https://app.leadteh.ru/api/v1/getContactVariables?api_token=$token&contact_id=$contact_id";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        echo 'Ошибка при получении переменных.';
    } else {
        $result = json_decode($response, true);
        echo json_encode($result);
    }
}
getContactVariables($token, $contact_id);
?>
```

---

## ✏️ Создание или обновление переменной

**Метод:** `POST`

```php
function setContactVariable($token, $contact_id, $name, $value) {
    $apiUrl = "https://app.leadteh.ru/api/v1/setContactVariable?api_token=$token&contact_id=$contact_id&name=$name&value=$value";

    $data = [
        "contact_id" => $contact_id,
        "name" => $name,
        "value" => $value
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        echo 'Ошибка при установке переменной.';
    } else {
        $result = json_decode($response, true);
        echo json_encode($result);
    }
}
setContactVariable($token, $contact_id, $name, $value);
?>
```

---

## 🗑 Удаление переменной

**Метод:** `POST`

```php
function deleteContactVariable($token, $contact_id, $name) {
    $apiUrl = "https://app.leadteh.ru/api/v1/deleteContactVariable?api_token=$token&contact_id=$contact_id&name=$name";

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");

    $response = curl_exec($ch);
    curl_close($ch);

    if ($response === false) {
        echo 'Ошибка при удалении переменной.';
    } else {
        $result = json_decode($response, true);
        echo json_encode($result);
    }
}
deleteContactVariable($token, $contact_id, $name);
?>
```
