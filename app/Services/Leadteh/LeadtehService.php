<?php

namespace App\Services\Leadteh;

use App\Exceptions\Leadteh\LeadtehApiException;
use App\Exceptions\Leadteh\LeadtehRateLimitException;
use App\Exceptions\Leadteh\LeadtehAccountException;
use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class LeadtehService
{
    private string $apiToken;
    private string $baseUrl;
    private int $rateLimitDelay;
    private static int $lastRequestTime = 0;
    private bool $refEnabled;
    private array $refererLevels = [];
    private int $refMinAmount;

    public function __construct()
    {
        $this->apiToken = config('services.leadteh.api_token');
        $this->baseUrl = config('services.leadteh.base_url');
        $this->rateLimitDelay = config('services.leadteh.rate_limit_delay');
        $this->refEnabled = (bool) config('services.leadteh.ref_enabled', false);
        $this->refererLevels = config('services.leadteh.ref_levels', []);
        $this->refMinAmount = (int) config('services.leadteh.ref_min_amount', 1000); // сумма в копейках, 10 руб = 1000 коп

        if (!$this->apiToken) {
            throw new LeadtehApiException('LeadTeh API token not configured');
        }
    }

    /**
     * Выполнить HTTP запрос к API LeadTeh с rate limiting
     */
    private function makeRequest(string $method, string $endpoint, array $data = []): array
    {
        $this->enforceRateLimit();

        $url = $this->baseUrl . ltrim($endpoint, '/');
        $params = array_merge($data, ['api_token' => $this->apiToken]);

        Log::info('LeadTeh API Request', [
            'method' => $method,
            'url' => $url,
            'params' => array_merge($params, ['api_token' => '[HIDDEN]'])
        ]);

        $response = Http::withHeaders([
            'X-Requested-With' => 'XMLHttpRequest',
        ])->$method($url, $params);

        if ($response->status() === 429) {
            throw new LeadtehRateLimitException();
        }

        if (!$response->successful()) {
            Log::error('LeadTeh API Error', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);
            throw new LeadtehApiException(
                'API request failed: ' . $response->body(),
                $response->status(),
                $response->json() ?? []
            );
        }

        $result = $response->json();
        Log::info('LeadTeh API Response', ['result' => $result]);

        return $result;
    }

    /**
     * Обеспечить соблюдение rate limit (2 запроса в секунду)
     */
    private function enforceRateLimit(): void
    {
        $currentTime = microtime(true) * 1000;
        $timeSinceLastRequest = $currentTime - self::$lastRequestTime;

        if ($timeSinceLastRequest < $this->rateLimitDelay) {
            $sleepTime = $this->rateLimitDelay - $timeSinceLastRequest;
            usleep($sleepTime * 1000);
        }

        self::$lastRequestTime = microtime(true) * 1000;
    }

    /**
     * Получить информацию об аккаунте
     */
    public function getMe(): array
    {
        return $this->makeRequest('get', 'me');
    }

    /**
     * Получить список контактов
     */
    public function getContacts(array $filters = []): array
    {
        return $this->makeRequest('get', 'contacts', $filters);
    }

    /**
     * Отправить сообщение контакту
     */
    public function sendMessage(int $contactId, string $message, int $botId = null): array
    {
        $data = [
            'contact_id' => $contactId,
            'message' => $message,
        ];

        if ($botId) {
            $data['bot_id'] = $botId;
        }

        return $this->makeRequest('post', 'send-message', $data);
    }

    /**
     * Отправить сообщение в WhatsApp
     */
    public function sendMessageToWhatsApp(int $contactId, string $message): array
    {
        return $this->makeRequest('post', 'send-whatsapp', [
            'contact_id' => $contactId,
            'message' => $message,
        ]);
    }

    /**
     * Получить счета контакта
     */
    public function getContactAccounts(int $contactId): array
    {
        return $this->makeRequest('get', 'contact-accounts', [
            'contact_id' => $contactId
        ]);
    }

    /**
     * Создать счет для контакта
     */
    public function createContactAccount(int $contactId, string $accountName, string $notes = ''): array
    {
        return $this->makeRequest('post', 'create-account', [
            'contact_id' => $contactId,
            'account_name' => $accountName,
            'notes' => $notes,
        ]);
    }

    /**
     * Начислить средства на счет контакта
     */
    public function creditContactAccount(int $contactId, string $accountName, float $amount, string $notes = ''): array
    {
        return $this->makeRequest('post', 'credit-account', [
            'contact_id' => $contactId,
            'account_name' => $accountName,
            'amount' => $amount,
            'notes' => $notes,
        ]);
    }

    /**
     * Списать средства со счета контакта
     */
    public function debitContactAccount(int $contactId, string $accountName, float $amount, string $notes = ''): array
    {
        return $this->makeRequest('post', 'debit-account', [
            'contact_id' => $contactId,
            'account_name' => $accountName,
            'amount' => $amount,
            'notes' => $notes,
        ]);
    }

    /**
     * Получить цепочку рефереров контакта
     */
    public function getReferrers(int $contactId, int $refererLevels = 3): array
    {
        return $this->makeRequest('get', 'referrers', [
            'contact_id' => $contactId,
            'levels' => $refererLevels,
        ]);
    }

    /**
     * Установить переменную контакта
     */
    public function setContactVariable(int $contactId, string $variableName, $value): array
    {
        return $this->makeRequest('post', 'set-variable', [
            'contact_id' => $contactId,
            'variable_name' => $variableName,
            'value' => $value,
        ]);
    }

    /**
     * Добавить тег к контакту
     */
    public function attachTagToContact(int $contactId, string $tag): array
    {
        return $this->makeRequest('post', 'attach-tag', [
            'contact_id' => $contactId,
            'tag' => $tag,
        ]);
    }

    /**
     * Создать счет с уведомлением
     */
    public function createAccountWithNotification(int $contactId, string $accountName, string $notes = ''): array
    {
        $result = $this->createContactAccount($contactId, $accountName, $notes);

        $this->sendMessage($contactId, "Создан новый счет: {$accountName}");

        return $result;
    }

    /**
     * Обработать реферальные вознаграждения
     */
    public function processReferralRewards(float $amount, int $contactId, array $additionalData = []): array
    {
        if (!$this->isReferralEnabled()) {
            Log::info('Referral system disabled', ['contact_id' => $contactId]);
            return ['status' => 'disabled', 'message' => 'Referral system is disabled'];
        }

        $minAmount = $this->refMinAmount;
        if ($amount < $minAmount) {
            Log::info('Amount below minimum for referral', [
                'amount' => $amount,
                'min_amount' => $minAmount,
                'contact_id' => $contactId
            ]);
            return ['status' => 'below_minimum', 'amount' => $amount, 'min_amount' => $minAmount];
        }

        Log::info('Processing referral rewards', [
            'amount' => $amount,
            'contact_id' => $contactId,
            'additional_data' => $additionalData
        ]);

        $rollbackOperations = [];

        try {
            // Получить цепочку рефереров
            $referrersResponse = $this->getReferrers($contactId, 3);
            $referrers = $referrersResponse['referrers'] ?? [];

            if (empty($referrers)) {
                Log::info('No referrers found', ['contact_id' => $contactId]);
                return ['status' => 'no_referrers', 'contact_id' => $contactId];
            }

            $rewards = [];

            // Обработать каждый уровень
            foreach ($referrers as $level => $referrer) {
                if (!isset($this->refererLevels[$level])) continue;

                $rewardPercent = $this->refererLevels[$level];
                $rewardAmount = ($amount * $rewardPercent) / 100;
                $referrerId = $referrer['contact_id'];

                Log::info('Processing referral reward', [
                    'level' => $level,
                    'referrer_id' => $referrerId,
                    'reward_percent' => $rewardPercent,
                    'reward_amount' => $rewardAmount
                ]);

                // Создать или получить счет "referral_rewards"
                try {
                    $this->createContactAccount($referrerId, 'referral_rewards', 'Счет для реферальных вознаграждений');
                } catch (LeadtehApiException $e) {
                    // Счет уже существует, продолжаем
                }

                // Начислить вознаграждение
                $paymentId = $additionalData['payment_id'] ?? 'unknown';
                $notes = "Вознаграждение за совершенную покупку рефералом {$contactId}, PaymentId: {$paymentId}, Уровень: {$level}";

                $creditResult = $this->creditContactAccount($referrerId, 'referral_rewards', $rewardAmount, $notes);

                $rollbackOperations[] = [
                    'type' => 'debit',
                    'contact_id' => $referrerId,
                    'account_name' => 'referral_rewards',
                    'amount' => $rewardAmount,
                    'notes' => "Откат начисления из-за ошибки в реферальной цепочке. Исходная операция: {$notes}"
                ];

                // Установить переменные
                $this->setContactVariable($referrerId, 'last_referral_reward', $rewardAmount);
                $this->setContactVariable($referrerId, 'last_referral_date', now()->toDateTimeString());
                $this->setContactVariable($referrerId, 'referral_from_contact', $contactId);

                $rewards[] = [
                    'level' => $level,
                    'referrer_id' => $referrerId,
                    'amount' => $rewardAmount,
                    'percent' => $rewardPercent,
                    'result' => $creditResult
                ];
            }

            // Отправить уведомления (только после успешного завершения всех операций)
            $this->notifyReferralChain($contactId, $rewards, $additionalData);

            Log::info('Referral rewards processed successfully', [
                'contact_id' => $contactId,
                'total_rewards' => count($rewards),
                'rewards' => $rewards
            ]);

            return [
                'status' => 'success',
                'contact_id' => $contactId,
                'amount' => $amount,
                'rewards' => $rewards,
                'total_referrers' => count($rewards)
            ];

        } catch (Exception $e) {
            Log::error('Error processing referral rewards, rolling back', [
                'contact_id' => $contactId,
                'error' => $e->getMessage(),
                'rollback_operations' => count($rollbackOperations)
            ]);

            // Откатить все операции
            foreach ($rollbackOperations as $operation) {
                try {
                    if ($operation['type'] === 'debit') {
                        $this->debitContactAccount(
                            $operation['contact_id'],
                            $operation['account_name'],
                            $operation['amount'],
                            $operation['notes']
                        );
                    }
                } catch (Exception $rollbackError) {
                    Log::error('Rollback operation failed', [
                        'operation' => $operation,
                        'error' => $rollbackError->getMessage()
                    ]);
                }
            }

            throw new LeadtehAccountException(
                'Failed to process referral rewards: ' . $e->getMessage(),
                0,
                ['contact_id' => $contactId, 'amount' => $amount]
            );
        }
    }

    /**
     * Уведомить цепочку рефереров
     */
    public function notifyReferralChain(int $contactId, array $rewards, array $additionalData = []): void
    {
        foreach ($rewards as $reward) {
            $message = "🎉 Вы получили реферальное вознаграждение {$reward['amount']} руб. за активность вашего реферала!";

            try {
                $this->sendMessage($reward['referrer_id'], $message);
                Log::info('Referral notification sent', [
                    'referrer_id' => $reward['referrer_id'],
                    'amount' => $reward['amount']
                ]);
            } catch (Exception $e) {
                Log::error('Failed to send referral notification', [
                    'referrer_id' => $reward['referrer_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    /**
     * Проверить, включена ли реферальная система
     */
    private function isReferralEnabled(): bool
    {
        return $this->refEnabled;
    }
}
