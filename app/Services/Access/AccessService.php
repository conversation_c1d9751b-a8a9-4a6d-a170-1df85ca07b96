<?php

namespace App\Services\Access;

use App\Models\User;
use App\Models\Announce;
use App\Models\Setting;
use App\Models\SniEndpoint;
use App\Services\HelperService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class AccessService
{
    public function __construct(
        private HelperService $helperService
    ) {}

    /**
     * Generate VLESS keys for a user based on their active subscription and available servers.
     */
    public function generateVlessKeys(User $user): string
    {
        // Get user's active subscription
        $subscription = $user->currentSubscription;
        if (!$subscription || !$subscription->isActive()) {
            Log::info("No active subscription found for user {$user->id}");
            return '';
        }

        // Get server pool from user's active server pools
        $serverPools = $user->serverPools()
                ->whereNull('user_server_assignments.released_at')
                ->with('servers')
                ->get();
        var_dump($serverPools->toArray());

        if ($serverPools->isEmpty()) {
            Log::warning("No active server pools found for user {$user->id}");
            return '';
        }

        // Use the first active server pool
        $serverPool = $serverPools->where('is_active', true)->first();

        // Get active servers in the pool
        $servers = $serverPool->activeServers()->get();
        if ($servers->isEmpty()) {
            Log::warning("No active servers found in pool {$serverPool->id}");
            return '';
        }

        $vlessLinks = [];

        foreach ($servers as $server) {
            // Get inbounds for this server
            $inbounds = $server->inbounds();

            foreach ($inbounds as $inbound) {
                if (!$inbound->enable) {
                    continue;
                }

                try {
                    $vlessUrl = $this->generateVlessUrl($user, $server, $inbound);
                    if ($vlessUrl) {
                        $vlessLinks[] = $vlessUrl;
                    }
                } catch (\Exception $e) {
                    Log::error("Error generating VLESS URL for user {$user->id}", [
                        'server' => $server->name,
                        'inbound' => $inbound->remark,
                        'error' => $e->getMessage(),
                    ]);
                }
            }
        }

        if (empty($vlessLinks)) {
            Log::warning("No VLESS links generated for user {$user->id}");
            return '';
        }

        return implode("\n", $vlessLinks);
    }

    /**
     * Generate a single VLESS URL for user, server, and inbound.
     */
    private function generateVlessUrl(User $user, $server, $inbound): string
    {
        $userUuid = $user->id;
        $serverAddress = $server->address;
        $serverPort = $inbound->port;

        // Build query parameters from inbound stream settings
        $queryParams = $this->buildQueryParamsFromStreamSettings($inbound->streamSettings, $user);

        // Build remark with server load info
        $customString = $this->getServerLoadString($server);
        $remark = urlencode($inbound->remark . $customString);

        // Construct VLESS URL
        $vlessUrl = "vless://{$userUuid}@{$serverAddress}:{$serverPort}";

        if (!empty($queryParams)) {
            $vlessUrl .= '?' . http_build_query($queryParams);
        }

        $vlessUrl .= "#{$remark}";

        return $vlessUrl;
    }

    private function getSniForUser(User $user)
    {
        $sniCategoryName = $user->isSniMessenger() ? 'messengers' : 'other_services';

        return SniEndpoint::whereHas('sniTags', function ($q) {
                        $q->where('name', 'optimal');
                    })->whereHas('provider.category', function ($q) use ($sniCategoryName) {
                        $q->where('name', $sniCategoryName);
                    })->pluck('domain')->first();
    }

    /**
     * Build query parameters from inbound stream settings.
     */
    private function buildQueryParamsFromStreamSettings(array $streamSettings, User $user): array
    {
        $params = [];

        // Network type
        if (isset($streamSettings['network'])) {
            $params['type'] = $streamSettings['network'];
        }

        // Security
        if (isset($streamSettings['security'])) {
            $params['security'] = $streamSettings['security'];
        }

        // Reality settings
        if (isset($streamSettings['realitySettings'])) {
            $reality = $streamSettings['realitySettings'];

            if (isset($reality['settings']['publicKey'])) {
                $params['pbk'] = $reality['settings']['publicKey'];
            }

            if (isset($reality['settings']['fingerprint'])) {
                $params['fp'] = $reality['settings']['fingerprint'];
            }

            if (isset($reality['serverNames']) && !empty($reality['serverNames'])) {
                $params['sni'] = $this->getSniForUser($user) ?? $reality['serverNames'][0];
            }

            if (isset($reality['settings']['shortId'])) {
                $params['sid'] = $reality['settings']['shortId'];
            }

            if (isset($reality['settings']['spiderX'])) {
                $params['spx'] = $reality['settings']['spiderX'];
            }
        }

        // Flow (for XTLS)
        $flow = Setting::get('default_flow', 'xtls-rprx-vision');
        if ($flow) {
            $params['flow'] = $flow;
        }

        return $params;
    }

    /**
     * Get server load string for display.
     */
    private function getServerLoadString($server): string
    {
        if ($server->server_load !== null) {
            $loadPercent = round($server->server_load, 1);
            return " ({$loadPercent}%)";
        }

        return '';
    }

    /**
     * Get subscription headers for VPN clients.
     */
    public function getSubscriptionHeaders(User $user, string $uuid): array
    {
        $announceData = $this->getAnnounceData($user, $uuid);

        // Determine routing rules based on user preference
        $routingRules = '';
        if ($user->use_common_routing) {
            $routingRules = Setting::get('common_routing_rules', '', true);
            if (!empty($routingRules)) {
                $routingRules = base64_encode($this->helperService->encodeJsonUtf8Safe($routingRules) ?? $routingRules);
            }
        }

        $headers = [
            'Profile-Title' => 'base64:' . base64_encode($this->getSubscriptionTitle($user)),
            'Content-Type' => 'text/plain; charset=utf-8',
            'Profile-Update-Interval' => '1', // update interval in hours
            'Profile-Web-Page-Url' => config('app.url') . '/access/' . $uuid,
            'Support-Url' => config('app.url') . '/access/support/' . $uuid,
            'Announce' => 'base64:' . base64_encode($announceData['text']),
            'Announce-Url' => $announceData['url'],
            'Update-always' => 'true',
            'Routing' => $routingRules,
        ];

        // Add subscription userinfo
        $userinfo = $this->buildSubscriptionUserinfo($user);
        if ($userinfo) {
            $headers['Subscription-Userinfo'] = $userinfo;
        }

        return $headers;
    }

    /**
     * Get subscription title.
     */
    private function getSubscriptionTitle(User $user): string
    {
        $title = config("app.subs_title", "SmartVPN");

        $title .= " \n       ID клиента: {$user->client_id} 🔰";

        return $title;
    }

    /**
     * Get announce data based on subscription status.
     */
    private function getAnnounceData(User $user, string $uuid): array
    {
        $defaultText = config("app.subs_announce", "");
        $defaultUrl = config("app.subs_announce_url", "");

        try {
            $defaultUrl = $this->helperService->replaceTextVariables($defaultUrl, $user);

            // Get current subscription
            $currentSubscription = $user->currentSubscription;
            $earliestExpiry = $currentSubscription?->end_date;

            if ($earliestExpiry) {
                $now = Carbon::now();
                $expiryTime = Carbon::parse($earliestExpiry);

                // Check if subscription has already expired
                if ($expiryTime->isPast()) {
                    return [
                        'text' => "❗️ #c11e14ПОДПИСКА #c11e14ИСТЕКЛА ❗️\n\nНажмите сюда, чтобы продлить подписку 💳",
                        'url' => config('app.url') . "/access/{$uuid}/plan/select",
                    ];
                }

                // Check if expiry is within 3 days
                if ($expiryTime->isFuture() && $now->diffInDays($expiryTime, false) < 3) {
                    $remainingTime = $this->helperService->formatRemainingTime($now, $expiryTime);

                    return [
                        'text' => "🪫 ПОДПИСКА #c11e14ЗАКОНЧИТСЯ через {$remainingTime}\n\n#c11e14Нажмите сюда, чтобы продлить подписку 💳",
                        'url' => config('app.url') . "/access/{$uuid}/plan/select",
                    ];
                }
            }

            // Get latest announce from database
            $latestAnnounce = Announce::getLatestVisible();

            if ($latestAnnounce) {
                $url = $this->helperService->replaceTextVariables($latestAnnounce->url, $user);

                return [
                    'text' => $latestAnnounce->message,
                    'url' => $url ?: $defaultUrl,
                ];
            }

            // Fallback to default
            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];

        } catch (\Exception $e) {
            Log::error("Error getting announce data for UUID: {$uuid}", [
                'error' => $e->getMessage(),
            ]);

            return [
                'text' => $defaultText,
                'url' => $defaultUrl,
            ];
        }
    }

    /**
     * Build subscription userinfo string.
     */
    private function buildSubscriptionUserinfo(User $user): string
    {
        $subscription = $user->currentSubscription;

        $totalUpload = $subscription?->traffic_up_bytes ?? 0;
        $totalDownload = $subscription?->traffic_down_bytes ?? 0;
        $totalLimit = $subscription?->plan?->traffic_limit_bytes ?? 0;

        // If there is no traffic consumption at all, specify at least 1 byte
        // so that the traffic scale starts to be displayed in v2ray applications
        if ($totalUpload === 0 && $totalDownload === 0) {
            $totalDownload = 1;
        }

        $userinfo = sprintf('upload=%d; download=%d', $totalUpload, $totalDownload);

        // Add total only if there are actual limits set
        if ($totalLimit > 0) {
            $userinfo .= sprintf('; total=%d', $totalLimit);
        }

        // Add expire if there's an expiry time
        if ($subscription && $subscription->end_date) {
            $expireTimestamp = $subscription->end_date->timestamp;
            $userinfo .= sprintf('; expire=%d', $expireTimestamp);
        }

        return $userinfo;
    }

    /**
     * Prepare profile data for the view.
     */
    public function prepareProfileData(User $user, string $uuid): array
    {
        $subscription = $user->currentSubscription;
        $vlessContent = $this->generateVlessKeys($user);

        // Parse vless links from content
        $vlessLinks = array_filter(explode("\n", $vlessContent));

        // Get statistics
        $totalUpload = $subscription?->traffic_up_bytes ?? 0;
        $totalDownload = $subscription?->traffic_down_bytes ?? 0;
        $totalLimit = $subscription?->plan?->traffic_limit_bytes ?? 0;
        $earliestExpiry = $subscription?->end_date;

        // Determine status
        $isExpired = $earliestExpiry && $earliestExpiry->isPast();
        $isLimited = $totalLimit > 0 && ($totalUpload + $totalDownload) >= $totalLimit;
        $status = $isLimited ? 'limited' : ($isExpired ? 'expired' : 'active');

        // Calculate remaining time
        $remainingTime = $this->calculateRemainingTime($earliestExpiry);

        return [
            'client_id' => $user->client_id,
            'email' => $user->email ?? 'User',
            'subscription_url' => config('app.url') . '/access/' . $uuid,
            'vless_links' => $vlessLinks,
            'vless_content' => $vlessContent,
            'up' => $totalUpload,
            'down' => $totalDownload,
            'total' => $totalLimit ?: 0,
            'used_traffic' => $totalUpload + $totalDownload,
            'data_limit' => $totalLimit ?: 0,
            'expiry_time' => $earliestExpiry,
            'remaining_time' => $remainingTime,
            'reset_interval' => 'none',
            'enable' => !$isExpired && !$isLimited,
            'status' => $status,
            'uuid' => $uuid,
            'use_common_routing' => $user->use_common_routing,
            'is_sni_messenger' => $user->isSniMessenger(),
            'subscription' => $subscription ? [
                'all' => $subscription->toArray(),
                'plan_name' => $subscription->plan->name,
                'is_active' => $subscription->isActive(),
                'is_expired' => $subscription->isExpired(),
                'started_at' => $subscription->start_date,
                'expires_at' => $subscription->end_date,
                'traffic_limit_gb' => $subscription->plan->traffic_limit_bytes ? round($subscription->plan->traffic_limit_bytes / (1024 * 1024 * 1024), 2) : null,
                'traffic_used_gb' => round(($totalUpload + $totalDownload) / (1024 * 1024 * 1024), 2),
                'remaining_days' => $subscription->getRemainingDays(),
                'traffic_usage_percentage' => $subscription->getTrafficUsagePercentage(),
                'renewal_required' => $subscription->isRenewalRequired(),
                'is_about_to_expire' => $subscription->isAboutToExpire(),
                'is_demo' => $subscription->isDemo(),
            ] : null,
        ];
    }

    /**
     * Calculate remaining time with appropriate units.
     */
    private function calculateRemainingTime($expiryTime): array
    {
        if (!$expiryTime || $expiryTime->isPast()) {
            return [
                'value' => 0,
                'unit' => 'expired',
                'display' => 'Истекла',
                'display_en' => 'Expired'
            ];
        }

        $now = now();
        $diff = $now->diff($expiryTime);

        $totalHours = $diff->days * 24 + $diff->h;

        // If less than 1 hour, show minutes
        if ($totalHours < 1) {
            $minutes = $diff->i;
            return [
                'value' => $minutes,
                'unit' => 'minutes',
                'display' => $minutes . ' мин.',
                'display_en' => $minutes . ' min.'
            ];
        }

        // If less than 24 hours, show hours and minutes
        if ($diff->days < 1) {
            $hours = $totalHours;
            $minutes = $diff->i;

            if ($minutes > 0) {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч. ' . $minutes . ' мин.',
                    'display_en' => $hours . ' h. ' . $minutes . ' min.'
                ];
            } else {
                return [
                    'value' => $hours,
                    'unit' => 'hours',
                    'display' => $hours . ' ч.',
                    'display_en' => $hours . ' h.'
                ];
            }
        }

        // Otherwise show days
        return [
            'value' => $diff->days,
            'unit' => 'days',
            'display' => $diff->days . ' дн.',
            'display_en' => $diff->days . ' days'
        ];
    }
}
