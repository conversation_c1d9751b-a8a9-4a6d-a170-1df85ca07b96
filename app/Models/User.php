<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasUuids, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'tg_id',
        'old_client_id',
        'email_verified_at',
        'password',
        'is_active',
        'registered_at',
        'referral_code',
        'referred_by_user_id',
        'notes',
        'admin_notes',
        'details',
        'source',
        'use_common_routing',
        'subscription_plan_id',
        'last_online_at',
        'sni_category',
    ];

    protected $appends = [
        'client_id',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'registered_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'use_common_routing' => 'boolean',
            'details' => 'array',
            'last_online_at' => 'datetime',
            'sni_category' => 'string',
        ];
    }


    /* ---------------- Relationships ---------------- */

    /**
     * Get the user who referred this user.
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referred_by_user_id');
    }

    /**
     * Get the users referred by this user.
     */
    public function referrals(): HasMany
    {
        return $this->hasMany(User::class, 'referred_by_user_id');
    }

    /**
     * Get the subscription plan for this user.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the subscription plan for this user. (alias for subscriptionPlan())
     */
    public function plan(): BelongsTo
    {
        return $this->subscriptionPlan();
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get the user's current active subscription.
     */
    public function currentSubscription(): HasOne
    {
        return $this->hasOne(Subscription::class)->where('status', 'active')->latest();
    }

    /**
     * Get the user's orders.
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the user's traffic logs.
     */
    public function trafficLogs(): HasMany
    {
        return $this->hasMany(UserTrafficLog::class);
    }

    /**
     * Get the user's online logs.
     */
    public function onlineLogs(): HasMany
    {
        return $this->hasMany(UserOnlineLog::class);
    }

    /**
     * Get the user's ratings.
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(UserRating::class);
    }

    /**
     * Get the server pools assigned to this user.
     */
    public function serverPools(): BelongsToMany
    {
        return $this->belongsToMany(ServerPool::class, 'user_server_assignments', 'user_id', 'pool_id')
            ->withPivot(['assigned_at', 'released_at'])
            ->withTimestamps();
    }

    /**
     * Get the active server pools assigned to this user.
     */
    public function activeServerPools(): BelongsToMany
    {
        return $this->serverPools()
                ->where('server_pools.is_active', true)
                ->wherePivotNull('released_at');
    }

    /**
     * Get the referral records where this user is the inviter.
     */
    public function invitedReferrals(): HasMany
    {
        return $this->hasMany(Referral::class, 'inviter_user_id');
    }

    /**
     * Get the referral record where this user was invited.
     */
    public function invitationReferral(): HasOne
    {
        return $this->hasOne(Referral::class, 'invited_user_id');
    }

    /**
     * Get the subscription history for this user.
     */
    public function subscriptionHistory(): HasMany
    {
        return $this->hasMany(SubscriptionHistory::class);
    }


    /* ---------------- Scopes ---------------- */

    /**
     * Scope to get only active users.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only users with active subscriptions.
     */
    public function scopeWithActiveSubscription($query)
    {
        return $query->whereHas('currentSubscription');
    }

    /**
     * Scope to get only users without active subscriptions.
     */
    public function scopeWithoutActiveSubscription($query)
    {
        return $query->doesntHave('currentSubscription');
    }


    /* ---------------- Accessors & Mutators ---------------- */

    /**
     * Generate client email for XUI (user_email + "_" + inbound_id). Antagen to User::extractEmailFromClientEmail()
     */
    public function generateClientEmailForInbound(int $inboundId): string
    {
        return $this->email . '_' . $inboundId;
    }

    /**
     * Get the client ID for this user.
     */
    public function getClientId()
    {
        return $this->old_client_id ?? $this->id;
    }

    /**
     * Get the appended field client_id for this user.
     */
    protected function getClientIdAttribute()
    {
        return $this->getClientId();
    }


    /* ---------------- State Checks ---------------- */

    /**
     * Check if the user is enabled.
     */
    public function isEnabled()
    {
        return $this->is_active && $this->currentSubscription()->exists();
    }

    /**
     * Check if the user is a messenger.
     */
    public function isSniMessenger(): bool
    {
        return $this->sni_category === 'messengers';
    }


    /* ---------------- Model Events / Hooks ---------------- */

    /**
     * Boot the model.
     */
    protected static function booted()
    {
        static::created(function (User $user) {
            // create a referral code for the user on creation
            $user->referral_code = $user->generateReferralCode();
            $user->save();
        });
    }


    /* ---------------- Actions ---------------- */

    /**
     * Activate the user.
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    /**
     * Deactivate the user.
     */
    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    /**
     * Upgrade the user to a new subscription plan.
     */
    public function upgradeSubscription(SubscriptionPlan $newPlan): void
    {
        // update the user's subscription plan
        $this->update(['subscription_plan_id' => $newPlan->id]);
    }

    /**
     * Change the user's subscription plan if it's different from the new plan.
     */
    public function changePlanIfDifferent(SubscriptionPlan $newPlan): void
    {
        if (!$this->subscriptionPlan || $this->subscriptionPlan->id !== $newPlan->id) {
            $this->upgradeSubscription($newPlan);
        }
    }


    /* ---------------- Helpers / Utilities ---------------- */

    /**
     * Generate a referral code for the user.
     */
    public function generateReferralCode(): string
    {
        return bin2hex(random_bytes(4));
    }

    /**
     * Extract email from client email. Antagen to User::generateClientEmailForInbound()
     */
    public static function extractEmailFromClientEmail(string $clientEmail, ?int $inboundId = null): string
    {
        if (preg_match('/^(.+)_\d+$/', $clientEmail, $matches)) {
            return $matches[1];
        }

        return Str::chopEnd($clientEmail, '_' . $inboundId);
    }


    /* ---------------- Aggregates / Stats ---------------- */


}
